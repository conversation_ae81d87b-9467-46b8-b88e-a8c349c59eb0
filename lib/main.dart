import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:provider/provider.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'firebase_options.dart';
import 'core/theme/app_theme.dart';
import 'core/providers/theme_provider.dart';
import 'core/services/screenshot_prevention_service.dart';
import 'core/services/app_icon_service.dart';
import 'core/services/local_storage_service.dart';
import 'core/services/api_key_service.dart';
import 'core/widgets/screenshot_prevention_wrapper.dart';
import 'core/widgets/splash_screen.dart';
import 'features/authentication/data/repositories/auth_repository.dart';
import 'features/authentication/presentation/bloc/auth_bloc.dart';
import 'features/authentication/presentation/bloc/auth_event.dart';
import 'features/authentication/presentation/bloc/auth_state.dart';
import 'features/authentication/presentation/pages/phone_auth_page.dart';
import 'features/banners/data/repositories/banner_repository.dart';
import 'features/banners/domain/entities/banner_item.dart';
import 'features/banners/domain/usecases/banner_service.dart';
import 'features/banners/presentation/pages/banner_selection_page.dart';
import 'features/settings/presentation/pages/web_view_page.dart';
import 'features/user/presentation/pages/profile_page.dart';
import 'features/templates/data/repositories/template_repository.dart';
import 'features/templates/domain/entities/template_item.dart';
import 'features/templates/domain/usecases/template_service.dart';
import 'features/templates/presentation/widgets/template_grid.dart';
import 'features/user/data/repositories/firebase_user_repository.dart';
import 'features/user/data/repositories/user_type_repository.dart';
import 'features/user/data/repositories/firebase_business_parameter_repository.dart';
import 'features/user/data/repositories/firebase_political_parameter_repository.dart';
import 'features/user/data/repositories/firebase_party_repository.dart';
import 'features/user/domain/repositories/user_repository.dart';
import 'features/user/domain/repositories/business_parameter_repository.dart';
import 'features/user/domain/repositories/political_parameter_repository.dart';
import 'features/user/domain/repositories/party_repository.dart';
import 'features/user/domain/entities/user_model.dart';
import 'features/user/domain/usecases/user_service.dart';
import 'features/user/domain/usecases/user_type_service.dart';
import 'features/user/domain/usecases/business_parameter_service.dart';
import 'features/user/domain/usecases/political_parameter_service.dart';
import 'features/user/domain/usecases/party_service.dart';
import 'features/user/presentation/pages/profile_completion_page.dart';
import 'features/user/presentation/pages/user_type_selection_page.dart';
import 'features/ai_image_generation/data/repositories/nscale_image_generation_repository.dart';
import 'features/ai_image_generation/domain/repositories/image_generation_repository.dart';
import 'features/ai_image_generation/domain/usecases/image_generation_service.dart';
import 'features/ai_image_generation/domain/usecases/ai_logo_generation_service.dart';
import 'features/ai_image_generation/presentation/pages/ai_image_generation_page.dart';
import 'features/subscription/domain/usecases/subscription_service.dart';
import 'features/subscription/presentation/pages/subscription_page.dart';
import 'features/subscription/presentation/pages/premium_trial_page.dart';
import 'features/admin/data/repositories/firebase_admin_repository.dart';
import 'features/admin/domain/repositories/admin_repository.dart';
import 'features/admin/domain/usecases/admin_service.dart';
import 'features/admin/presentation/pages/admin_dashboard_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables
  await dotenv.load(fileName: ".env");

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize Firebase App Check
  await FirebaseAppCheck.instance.activate(
    // Use debug provider in development
    // In production, this should be a real provider like AndroidProvider or AppleProvider
    androidProvider: AndroidProvider.debug,
    appleProvider: AppleProvider.debug,
  );

  // Initialize local storage service
  await LocalStorageService.initialize();

  // Initialize API key service
  await ApiKeyService.initialize();

  // Initialize screenshot prevention service
  await ScreenshotPreventionService().initialize();

  // Initialize app icon service
  debugPrint('Initializing app icon service...');
  await AppIconService.initialize();
  debugPrint('App icon service initialized');

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Create and provide the UserRepository
        Provider<UserRepository>(
          create: (context) => FirebaseUserRepository(),
        ),

        // Create and provide the UserTypeRepository
        Provider<UserTypeRepository>(
          create: (context) => UserTypeRepository(),
        ),

        // Create and provide the UserService
        ProxyProvider<UserRepository, UserService>(
          update: (context, userRepository, previous) =>
              UserService(userRepository),
        ),

        // Create and provide the UserTypeService
        ProxyProvider<UserTypeRepository, UserTypeService>(
          update: (context, userTypeRepository, previous) =>
              UserTypeService(userTypeRepository),
        ),

        // Create and provide the BusinessParameterRepository
        Provider<BusinessParameterRepository>(
          create: (context) => FirebaseBusinessParameterRepository(),
        ),

        // Create and provide the BusinessParameterService
        ProxyProvider<BusinessParameterRepository, BusinessParameterService>(
          update: (context, repository, previous) =>
              BusinessParameterService(repository),
        ),

        // Create and provide the PoliticalParameterRepository
        Provider<PoliticalParameterRepository>(
          create: (context) => FirebasePoliticalParameterRepository(),
        ),

        // Create and provide the PoliticalParameterService
        ProxyProvider<PoliticalParameterRepository, PoliticalParameterService>(
          update: (context, repository, previous) =>
              PoliticalParameterService(repository),
        ),

        // Create and provide the PartyRepository
        Provider<PartyRepository>(
          create: (context) => FirebasePartyRepository(),
        ),

        // Create and provide the PartyService
        ProxyProvider<PartyRepository, PartyService>(
          update: (context, repository, previous) =>
              PartyService(repository),
        ),

        // Create and provide the TemplateRepository
        Provider<TemplateRepository>(
          create: (context) => TemplateRepository(),
        ),

        // Create and provide the TemplateService
        ProxyProvider<TemplateRepository, TemplateService>(
          update: (context, templateRepository, previous) =>
              TemplateService(templateRepository),
        ),

        // Create and provide the BannerRepository
        Provider<BannerRepository>(
          create: (context) => BannerRepository(),
        ),

        // Create and provide the BannerService
        ProxyProvider<BannerRepository, BannerService>(
          update: (context, bannerRepository, previous) =>
              BannerService(bannerRepository),
        ),

        // Create and provide the AuthBloc
        ProxyProvider<UserService, AuthBloc>(
          update: (context, userService, previous) => AuthBloc(
            authRepository: AuthRepository(),
            userService: userService,
          )..add(previous == null ? CheckAuthStatusEvent() : const NoOpAuthEvent()),
        ),

        // Provide the ScreenshotPreventionService
        Provider<ScreenshotPreventionService>(
          create: (context) => ScreenshotPreventionService(),
        ),

        // Provide the ThemeProvider
        ChangeNotifierProxyProvider<UserService, ThemeProvider>(
          create: (context) => ThemeProvider(
            Provider.of<UserService>(context, listen: false),
          ),
          update: (context, userService, previous) =>
              previous ?? ThemeProvider(userService),
        ),

        // Create and provide the ImageGenerationRepository
        Provider<ImageGenerationRepository>(
          create: (context) => NScaleImageGenerationRepository(),
        ),

        // Create and provide the ImageGenerationService
        ProxyProvider<ImageGenerationRepository, ImageGenerationService>(
          update: (context, repository, previous) =>
              previous ?? ImageGenerationService(repository),
        ),

        // Create and provide the AILogoGenerationService
        ProxyProvider<ImageGenerationRepository, AILogoGenerationService>(
          update: (context, repository, previous) =>
              previous ?? AILogoGenerationService(repository),
        ),

        // Create and provide the SubscriptionService
        ProxyProvider<UserService, SubscriptionService>(
          update: (context, userService, previous) =>
              previous ?? SubscriptionService(userService),
        ),

        // Create and provide the AdminRepository
        Provider<AdminRepository>(
          create: (context) => FirebaseAdminRepository(),
        ),

        // Create and provide the AdminService
        ProxyProvider<AdminRepository, AdminService>(
          update: (context, adminRepository, previous) =>
              previous ?? AdminService(adminRepository),
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, _) => MaterialApp(
          title: 'Quick Posters',
          theme: themeProvider.currentTheme,
          // Localization setup
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en', ''), // English
            Locale('hi', ''), // Hindi
            Locale('mr', ''), // Marathi
          ],
          initialRoute: '/',
          onGenerateRoute: (settings) {
            if (settings.name == '/home') {
              return MaterialPageRoute(builder: (_) => const HomePage());
            } else if (settings.name == '/admin') {
              return MaterialPageRoute(builder: (_) => const AdminDashboardPage());
            }
            return null;
          },
          home: SplashScreen(
            child: BlocBuilder<AuthBloc, AuthState>(
              builder: (context, state) {
                Widget homeWidget;

                if (state is AuthSuccess) {
                  if (state.isNewUser) {
                    // New user needs to complete profile
                    homeWidget = const ProfileCompletionPage();
                  } else {
                    // Existing user with complete profile
                    homeWidget = const HomePage();
                  }
                } else {
                  // User is not authenticated, show login page
                  homeWidget = const PhoneAuthPage();
                }

                // Wrap with screenshot prevention
                return ScreenshotPreventionWrapper(
                  child: homeWidget,
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  bool _isLoadingTemplates = true;
  bool _isLoadingBanners = true;
  bool _isLoadingMoreTemplates = false;
  bool _isCheckingUserType = true;
  bool _isLoadingCategories = true;
  List<TemplateItem> _templates = [];
  List<BannerItem> _banners = [];
  BannerItem? _selectedBanner;
  List<String> _categories = [];
  String _selectedCategory = 'All';


  // This initState method is replaced by the one below

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Set the selected banner when banners are loaded
    if (_banners.isNotEmpty && _selectedBanner == null) {
      setState(() {
        _selectedBanner = _banners.first;
      });
    }
  }

  Future<void> _loadCategories() async {
    try {
      final templateService = Provider.of<TemplateService>(context, listen: false);

      if (mounted) {
        setState(() {
          _isLoadingCategories = true;
        });
      }

      final categories = await templateService.getAvailableCategories();

      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoadingCategories = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingCategories = false;
        });
      }
    }
  }

  Future<void> _loadTemplates({bool refresh = false}) async {
    try {
      final templateService = Provider.of<TemplateService>(context, listen: false);

      // Reset pagination if refreshing or category changed
      if (refresh) {
        templateService.resetPagination();
        if (mounted) {
          setState(() {
            _templates = [];
          });
        }
      }

      // Set loading state
      if (mounted) {
        setState(() {
          if (refresh || _templates.isEmpty) {
            _isLoadingTemplates = true;
          } else {
            _isLoadingMoreTemplates = true;
          }
        });
      }

      // Load templates with pagination and category filter
      final category = _selectedCategory == 'All' ? null : _selectedCategory;
      final templates = await templateService.getTemplates(limit: 5, category: category);

      if (mounted) {
        setState(() {
          // Add new templates to existing list
          _templates.addAll(templates);
          _isLoadingTemplates = false;
          _isLoadingMoreTemplates = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingTemplates = false;
          _isLoadingMoreTemplates = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load templates')),
        );
      }
      // Rethrow the exception so the caller (like _refreshAll) can handle it
      rethrow;
    }
  }

  // Load more templates when scrolling to the end
  Future<void> _loadMoreTemplates() async {
    if (_isLoadingMoreTemplates || _isLoadingTemplates) {
      return; // Already loading
    }

    await _loadTemplates();
  }

  Future<void> _loadBanners({bool refresh = false}) async {
    try {
      final bannerService = Provider.of<BannerService>(context, listen: false);

      // Reset pagination if refreshing
      if (refresh) {
        bannerService.resetPagination();
        if (mounted) {
          setState(() {
            _banners = [];
          });
        }
      }

      // Set loading state
      if (mounted && (refresh || _banners.isEmpty)) {
        setState(() {
          _isLoadingBanners = true;
        });
      }

      // Load banners with pagination
      final banners = await bannerService.getBanners(limit: 5);

      if (mounted) {
        setState(() {
          // Add new banners to existing list
          _banners.addAll(banners);
          _isLoadingBanners = false;

          // Set the first banner as selected if we have banners and none is selected yet
          if (_banners.isNotEmpty && _selectedBanner == null) {
            _selectedBanner = _banners.first;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingBanners = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to load banners')),
        );
      }
      // Rethrow the exception so the caller (like _refreshAll) can handle it
      rethrow;
    }
  }

  void _onTemplateTap(TemplateItem template) {
    // Just show a message when template is tapped
    // We'll only navigate to the editor when the Create Poster button is tapped
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Template selected: ${template.name}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _onCategorySelected(String category) {
    if (_selectedCategory != category) {
      setState(() {
        _selectedCategory = category;
        _templates = []; // Clear current templates
      });

      // Load templates for the new category
      _loadTemplates(refresh: true);
    }
  }

  Future<void> _refreshAll() async {
    try {
      // Load categories, templates and banners in parallel with refresh flag
      await Future.wait([
        _loadCategories(),
        _loadTemplates(refresh: true),
        _loadBanners(refresh: true),
      ]);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Content refreshed successfully'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      // Show error message if refresh fails
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to refresh content'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _checkUserType() async {
    setState(() {
      _isCheckingUserType = true;
    });

    try {
      final userService = Provider.of<UserService>(context, listen: false);
      final hasUserType = await userService.hasUserType();

      if (mounted && !hasUserType) {
        // User hasn't selected a user type yet, show the selection page
        setState(() {
          _isCheckingUserType = false;
        });

        // Navigate to user type selection page
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const UserTypeSelectionPage(),
          ),
        );
      } else if (mounted) {
        setState(() {
          _isCheckingUserType = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isCheckingUserType = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to check user type: $e')),
        );
      }
    }
  }

  void _showBannerSelectionPage() {
    // Show banner selection page
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BannerSelectionPage(
          banners: _banners,
          selectedBanner: _selectedBanner,
          onBannerSelected: (selectedBanner) {
            setState(() {
              _selectedBanner = selectedBanner;
            });
          },
        ),
      ),
    );
  }

  // Build drawer header with user info
  Widget _buildDrawerHeader() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return DrawerHeader(
      decoration: BoxDecoration(
        gradient: isPremium
            ? AppTheme.premiumGoldBlackGradient
            : AppTheme.primaryGradient,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(50),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: FutureBuilder<UserModel?>(
        future: context.read<UserService>().getCurrentUser(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          final user = snapshot.data;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // App logo
              Row(
                children: [
                  Image.asset(
                    isPremium ? 'assets/icons/premium_logo.png' : 'assets/icons/logo.png',
                    height: 40,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'QuickPosters',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.0,
                    ),
                  ),
                ],
              ),
              const Spacer(),

              // User info
              if (user != null) ...[
                Row(
                  children: [
                    // User avatar
                    CircleAvatar(
                      radius: 24,
                      backgroundColor: Colors.white,
                      backgroundImage: user.photoUrl != null
                          ? NetworkImage(user.photoUrl!)
                          : null,
                      child: user.photoUrl == null
                          ? const Icon(Icons.person, color: Colors.grey)
                          : null,
                    ),
                    const SizedBox(width: 12),

                    // User name and type
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            user.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (user.userType != null)
                            Text(
                              _getUserTypeLabel(user.userType!),
                              style: TextStyle(
                                color: Colors.white.withAlpha(200),
                                fontSize: 12,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  // Get user type label
  String _getUserTypeLabel(String userType) {
    switch (userType) {
      case 'businessman':
        return 'Business User';
      case 'politician':
        return 'Political User';
      default:
        return 'Regular User';
    }
  }

  // Build a section title
  Widget _buildSectionTitle(String title) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Padding(
      padding: const EdgeInsets.only(top: 16.0, bottom: 8.0, left: 16.0),
      child: Text(
        title,
        style: TextStyle(
          color: isPremium ? AppTheme.premiumGold : AppTheme.secondaryText,
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  // Build category chips
  Widget _buildCategoryChips() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    if (_isLoadingCategories) {
      return Container(
        height: 50,
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_categories.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = category == _selectedCategory;

          return Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: FilterChip(
              label: Text(
                category,
                style: TextStyle(
                  color: isSelected
                      ? (isPremium ? Colors.black : Colors.white)
                      : (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue),
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  _onCategorySelected(category);
                }
              },
              backgroundColor: isPremium
                  ? AppTheme.premiumDarkGrey.withAlpha(100)
                  : Colors.grey.withAlpha(50),
              selectedColor: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              checkmarkColor: isPremium ? Colors.black : Colors.white,
              side: BorderSide(
                color: isSelected
                    ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                    : (isPremium ? AppTheme.premiumGold.withAlpha(100) : AppTheme.primaryBlue.withAlpha(100)),
                width: 1.5,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          );
        },
      ),
    );
  }

  // Build a drawer item
  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? iconColor,
    Color? titleColor,
  }) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return ListTile(
      leading: Icon(
        icon,
        color: iconColor ?? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          color: titleColor ?? (isPremium ? AppTheme.premiumGold : null),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: isPremium
            ? TextStyle(color: Colors.white.withAlpha(200)) // 0.8 opacity white
            : null,
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      tileColor: isPremium ? AppTheme.premiumDarkGrey.withAlpha(100) : null,
    );
  }



  // Show logout confirmation dialog
  void _showLogoutConfirmationDialog(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final isPremium = themeProvider.isPremium;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: isPremium ? AppTheme.premiumBlack : null,
          title: Text(
            'Logout',
            style: TextStyle(
              color: isPremium ? AppTheme.premiumGold : null,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: TextStyle(
              color: isPremium ? Colors.white : null,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: isPremium
                ? BorderSide(color: AppTheme.premiumGold, width: 1)
                : BorderSide.none,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close the dialog
              },
              style: TextButton.styleFrom(
                foregroundColor: isPremium ? AppTheme.premiumGold : null,
              ),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close the dialog
                context.read<AuthBloc>().add(SignOutEvent());
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }

  // Build the drawer widget
  Widget _buildDrawer(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Drawer(
      child: Container(
        decoration: isPremium
            ? BoxDecoration(
                gradient: AppTheme.premiumGoldBlackGradient,
              )
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            // Drawer header with user info
            _buildDrawerHeader(),

            // Account section
            _buildSectionTitle('Account'),
            _buildDrawerItem(
              icon: Icons.person,
              title: 'My Profile',
              subtitle: 'View and edit your profile',
              onTap: () {
                Navigator.pop(context); // Close drawer
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ProfilePage(),
                  ),
                );
              },
            ),
            const Divider(),

            // App settings section
            _buildSectionTitle('App Settings'),
            _buildDrawerItem(
              icon: Icons.language,
              title: 'Language',
              subtitle: 'Change app language',
              onTap: () {
                Navigator.pop(context); // Close drawer
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Language settings coming soon')),
                );
              },
            ),
            _buildDrawerItem(
              icon: Icons.notifications,
              title: 'Notifications',
              subtitle: 'Manage notification settings',
              onTap: () {
                Navigator.pop(context); // Close drawer
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Notification settings coming soon')),
                );
              },
            ),
            // Premium subscription
            _buildDrawerItem(
              icon: Icons.workspace_premium,
              title: 'Premium Subscription',
              subtitle: isPremium ? 'You are a premium user' : 'Upgrade to premium',
              onTap: () {
                Navigator.pop(context); // Close drawer
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SubscriptionPage(),
                  ),
                );
              },
              iconColor: isPremium ? AppTheme.premiumGold : null,
              titleColor: isPremium ? AppTheme.premiumGold : null,
            ),
            const Divider(),

            // Tools section
            _buildSectionTitle('Tools'),
            _buildDrawerItem(
              icon: Icons.image,
              title: 'AI Image Generator',
              subtitle: 'Create images with AI',
              onTap: () {
                Navigator.pop(context); // Close drawer
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AIImageGenerationPage(),
                  ),
                );
              },
            ),
            // Admin access (only show for admin users)
            FutureBuilder<bool>(
              future: Provider.of<UserService>(context, listen: false).isCurrentUserAdmin(),
              builder: (context, snapshot) {
                if (snapshot.data == true) {
                  return _buildDrawerItem(
                    icon: Icons.admin_panel_settings,
                    title: 'Admin Dashboard',
                    subtitle: 'Manage app content and users',
                    onTap: () {
                      Navigator.pop(context); // Close drawer
                      Navigator.pushNamed(context, '/admin');
                    },
                    iconColor: AppTheme.errorRed,
                    titleColor: AppTheme.errorRed,
                  );
                }
                return const SizedBox.shrink();
              },
            ),
            const Divider(),

            // About section
            _buildSectionTitle('About'),
            _buildDrawerItem(
              icon: Icons.info,
              title: 'About QuickPosters',
              subtitle: 'Version 1.0.0',
              onTap: () {
                Navigator.pop(context); // Close drawer
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('About page coming soon')),
                );
              },
            ),
            _buildDrawerItem(
              icon: Icons.privacy_tip,
              title: 'Privacy Policy',
              subtitle: 'Read our privacy policy',
              onTap: () {
                Navigator.pop(context); // Close drawer
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const WebViewPage(
                      url: 'https://quickposters.in/privacy-policy.html',
                      title: 'Privacy Policy',
                    ),
                  ),
                );
              },
            ),
            _buildDrawerItem(
              icon: Icons.description,
              title: 'Terms of Service',
              subtitle: 'Read our terms of service',
              onTap: () {
                Navigator.pop(context); // Close drawer
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const WebViewPage(
                      url: 'https://quickposters.in/terms-of-service.html',
                      title: 'Terms of Service',
                    ),
                  ),
                );
              },
            ),
            _buildDrawerItem(
              icon: Icons.receipt_long,
              title: 'Refund Policy',
              subtitle: 'Read our refund policy',
              onTap: () {
                Navigator.pop(context); // Close drawer
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const WebViewPage(
                      url: 'https://quickposters.in/refund-policy.html',
                      title: 'Refund Policy',
                    ),
                  ),
                );
              },
            ),
            const Divider(),

            // Logout section
            _buildSectionTitle('Account Actions'),
            _buildDrawerItem(
              icon: Icons.logout,
              title: 'Logout',
              subtitle: 'Sign out from your account',
              onTap: () {
                Navigator.pop(context); // Close drawer
                _showLogoutConfirmationDialog(context);
              },
              iconColor: Colors.red,
              titleColor: Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();

    // Load categories, templates and banners
    _loadCategories();
    _loadTemplates();
    _loadBanners();
    _checkUserType();

    // Show premium trial page after a short delay if user is not premium
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowPremiumTrial();
    });
  }

  /// Check if user is not premium and show the premium trial page
  Future<void> _checkAndShowPremiumTrial() async {
    try {
      final userService = Provider.of<UserService>(context, listen: false);
      final isPremium = await userService.isPremiumUser();

      // Only show to non-premium users
      if (!isPremium && mounted) {
        // Show the premium trial page
        await Future.delayed(const Duration(seconds: 2));
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const PremiumTrialPage(),
            ),
          );
        }
      }
    } catch (e) {
      // Silently fail
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isLoading = _isLoadingTemplates || _isLoadingBanners || _isCheckingUserType;
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Quick Posters',
      ),
      drawer: _buildDrawer(context),
      // Body with banners and templates
      body: Container(
        decoration: isPremium
            ? BoxDecoration(
                gradient: AppTheme.premiumGoldBlackGradient,
              )
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: Column(
          children: [
            // Premium trial banner (only for non-premium users)
            if (!isPremium)
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PremiumTrialPage(),
                    ),
                  );
                },
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [
                        Colors.black,
                        Color(0xFF222222),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(76),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.workspace_premium,
                        color: AppTheme.premiumGold,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Try Premium for just ₹9',
                              style: TextStyle(
                                color: AppTheme.premiumGold,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            Text(
                              '8-day trial with all premium features',
                              style: TextStyle(
                                color: Colors.white.withAlpha(230),
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.premiumGold,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Text(
                          'Try Now',
                          style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Category filter chips
            _buildCategoryChips(),

            // Main content with refresh indicator
            Expanded(
              child: RefreshIndicator(
                onRefresh: _refreshAll,
                color: themeProvider.isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                backgroundColor: themeProvider.isPremium ? AppTheme.premiumDarkGrey : Colors.white,
                displacement: 40.0, // Increase displacement for better visibility
                strokeWidth: 3.0, // Make the indicator thicker
                edgeOffset: 0, // Start from the very top
                triggerMode: RefreshIndicatorTriggerMode.anywhere, // Allow triggering from anywhere in the list
                child: isLoading
                  ? ListView( // Use ListView so you can still pull-to-refresh while loading
                      physics: const AlwaysScrollableScrollPhysics(),
                      children: [
                        SizedBox(
                          height: MediaQuery.of(context).size.height * 0.6,
                          child: const Center(child: CircularProgressIndicator()),
                        )
                      ],
                    )
                  : (_templates.isEmpty && _banners.isEmpty)
                    ? ListView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        children: [
                          SizedBox(height: MediaQuery.of(context).size.height/2 - 100),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text(
                                'No content available',
                                style: TextStyle(fontSize: 16),
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _refreshAll,
                                child: const Text('Refresh'),
                              ),
                              const SizedBox(height: 20),
                              const Text(
                                'Pull down to refresh',
                                style: TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                            ],
                          ),
                        ],
                      )
                    : TemplateGrid(
                        templates: _templates,
                        onTemplateTap: _onTemplateTap,
                        banner: _selectedBanner,
                        onBannerTap: (_) => _showBannerSelectionPage(),
                        isLoading: false,
                        hasMoreTemplates: Provider.of<TemplateService>(context).hasMoreTemplates,
                        isLoadingMore: _isLoadingMoreTemplates,
                        onLoadMore: _loadMoreTemplates,
                      ),
                ),
              ),
            ])
          ),
        );
    }
  }

