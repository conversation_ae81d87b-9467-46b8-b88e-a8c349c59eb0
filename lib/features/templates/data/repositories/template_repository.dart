import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../../../core/utils/logger.dart';
import '../../../templates/domain/entities/template_item.dart';

class TemplateRepository {
  final FirebaseStorage _storage;
  final FirebaseFirestore _firestore;
  DocumentSnapshot? _lastDocument; // Track the last document for pagination
  bool _hasMoreTemplates = true; // Flag to indicate if more templates are available

  TemplateRepository({
    FirebaseStorage? storage,
    FirebaseFirestore? firestore,
  }) : _storage = storage ?? FirebaseStorage.instance,
       _firestore = firestore ?? FirebaseFirestore.instance;

  /// Reset pagination state
  void resetPagination() {
    _lastDocument = null;
    _hasMoreTemplates = true;
  }

  /// Check if more templates are available
  bool get hasMoreTemplates => _hasMoreTemplates;

  /// Fetches templates from Firestore with pagination and optional category filtering
  Future<List<TemplateItem>> getTemplates({
    int limit = 5,
    String? category,
  }) async {
    try {
      // If no more templates are available, return empty list
      if (!_hasMoreTemplates) {
        return [];
      }

      // Build query
      Query query = _firestore.collection('templates');

      // Add category filter first if specified
      if (category != null && category.isNotEmpty && category.toLowerCase() != 'all') {
        query = query.where('category', isEqualTo: category);
      }

      // Add isActive filter
      query = query.where('isActive', isEqualTo: true);

      // Add ordering
      query = query.orderBy('uploadDate', descending: true);

      // Add pagination
      if (_lastDocument != null) {
        query = query.startAfterDocument(_lastDocument!);
      }

      // Apply limit
      query = query.limit(limit);

      AppLogger.info('Fetching templates with limit: $limit, category: $category');

      final querySnapshot = await query.get();

      // Update pagination state
      if (querySnapshot.docs.isEmpty || querySnapshot.docs.length < limit) {
        _hasMoreTemplates = false;
        AppLogger.info('No more templates available');
      } else {
        _lastDocument = querySnapshot.docs.last;
        AppLogger.info('Last document ID: ${_lastDocument!.id}');
      }

      // Convert documents to TemplateItem objects
      final List<TemplateItem> templates = querySnapshot.docs
          .map((doc) => TemplateItem.fromFirestore(doc))
          .toList();

      AppLogger.info('Fetched ${templates.length} templates');
      return templates;
    } catch (e) {
      AppLogger.error('Failed to fetch templates', e);
      return [];
    }
  }

  /// Get unique categories from all templates
  Future<List<String>> getAvailableCategories() async {
    try {
      final querySnapshot = await _firestore
          .collection('templates')
          .where('isActive', isEqualTo: true)
          .get();

      // Extract unique categories
      final Set<String> categories = {};
      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        final category = data['category'] as String?;
        if (category != null && category.isNotEmpty) {
          categories.add(category);
        }
      }

      // Convert to list and sort
      final List<String> sortedCategories = categories.toList()..sort();

      AppLogger.info('Found ${sortedCategories.length} unique categories: $sortedCategories');
      return sortedCategories;
    } catch (e) {
      AppLogger.error('Failed to fetch categories', e);
      return [];
    }
  }

  /// Legacy method for backward compatibility - fetches template images from Firebase Storage
  Future<List<String>> getTemplateImages({int limit = 5}) async {
    try {
      // Get templates from Firestore instead
      final templates = await getTemplates(limit: limit);
      return templates.map((template) => template.imageUrl).toList();
    } catch (e) {
      AppLogger.error('Failed to fetch template images', e);
      return [];
    }
  }

  /// Gets a specific template image by name
  Future<String?> getTemplateImageByName(String imageName) async {
    try {
      final ref = _storage.ref().child('Templates/$imageName');
      return await ref.getDownloadURL();
    } catch (e) {
      AppLogger.error('Failed to fetch template image: $imageName', e);
      return null;
    }
  }
}
