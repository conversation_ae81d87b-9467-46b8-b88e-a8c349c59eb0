import '../entities/template_item.dart';
import '../../data/repositories/template_repository.dart';

class TemplateService {
  final TemplateRepository _templateRepository;
  String? _currentCategory; // Track current category filter

  TemplateService(this._templateRepository);

  /// Reset pagination state
  void resetPagination() {
    _templateRepository.resetPagination();
  }

  /// Check if more templates are available
  bool get hasMoreTemplates => _templateRepository.hasMoreTemplates;

  /// Get current category filter
  String? get currentCategory => _currentCategory;

  /// Set category filter
  void setCategory(String? category) {
    if (_currentCategory != category) {
      _currentCategory = category;
      resetPagination(); // Reset pagination when category changes
    }
  }

  /// Get templates with pagination and optional category filtering
  Future<List<TemplateItem>> getTemplates({int limit = 5, String? category}) async {
    // Update category filter if provided
    if (category != null) {
      setCategory(category);
    }

    final List<String> imageUrls = await _templateRepository.getTemplateImages(limit: limit);

    // Convert URLs to TemplateItem objects
    List<TemplateItem> templates = imageUrls.map((url) => TemplateItem.fromStorageUrl(url)).toList();

    // Filter by category if specified
    if (_currentCategory != null && _currentCategory!.isNotEmpty && _currentCategory!.toLowerCase() != 'all') {
      templates = templates.where((template) =>
        template.category?.toLowerCase() == _currentCategory!.toLowerCase()
      ).toList();
    }

    return templates;
  }

  /// Get all available templates (legacy method, use getTemplates with pagination instead)
  Future<List<TemplateItem>> getAllTemplates() async {
    // Reset pagination to ensure we get all templates from the beginning
    resetPagination();

    // Get first batch of templates
    List<TemplateItem> allTemplates = await getTemplates(limit: 100);

    return allTemplates;
  }

  /// Get templates by category
  Future<List<TemplateItem>> getTemplatesByCategory(String category) async {
    setCategory(category);
    return await getTemplates(limit: 100);
  }

  /// Get unique categories from all templates
  Future<List<String>> getAvailableCategories() async {
    final List<TemplateItem> allTemplates = await getAllTemplates();

    // Extract unique categories
    final Set<String> categories = {};
    for (final template in allTemplates) {
      if (template.category != null && template.category!.isNotEmpty) {
        categories.add(template.category!);
      }
    }

    // Convert to list and sort
    final List<String> sortedCategories = categories.toList()..sort();

    // Add "All" at the beginning
    return ['All', ...sortedCategories];
  }

  /// Get free templates only
  Future<List<TemplateItem>> getFreeTemplates() async {
    final List<TemplateItem> allTemplates = await getAllTemplates();

    // Filter out premium templates
    return allTemplates.where((template) => !template.isPremium).toList();
  }
}
