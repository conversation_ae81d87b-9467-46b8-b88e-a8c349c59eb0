import '../entities/template_item.dart';
import '../../data/repositories/template_repository.dart';

class TemplateService {
  final TemplateRepository _templateRepository;

  TemplateService(this._templateRepository);

  /// Reset pagination state
  void resetPagination() {
    _templateRepository.resetPagination();
  }

  /// Check if more templates are available
  bool get hasMoreTemplates => _templateRepository.hasMoreTemplates;

  /// Get templates with pagination
  Future<List<TemplateItem>> getTemplates({int limit = 5}) async {
    final List<String> imageUrls = await _templateRepository.getTemplateImages(limit: limit);

    // Convert URLs to TemplateItem objects
    return imageUrls.map((url) => TemplateItem.fromStorageUrl(url)).toList();
  }

  /// Get all available templates (legacy method, use getTemplates with pagination instead)
  Future<List<TemplateItem>> getAllTemplates() async {
    // Reset pagination to ensure we get all templates from the beginning
    resetPagination();

    // Get first batch of templates
    List<TemplateItem> allTemplates = await getTemplates(limit: 100);

    return allTemplates;
  }

  /// Get templates by category
  Future<List<TemplateItem>> getTemplatesByCategory(String category) async {
    final List<TemplateItem> allTemplates = await getAllTemplates();

    // Filter templates by category
    return allTemplates.where((template) =>
      template.category?.toLowerCase() == category.toLowerCase()
    ).toList();
  }

  /// Get free templates only
  Future<List<TemplateItem>> getFreeTemplates() async {
    final List<TemplateItem> allTemplates = await getAllTemplates();

    // Filter out premium templates
    return allTemplates.where((template) => !template.isPremium).toList();
  }
}
